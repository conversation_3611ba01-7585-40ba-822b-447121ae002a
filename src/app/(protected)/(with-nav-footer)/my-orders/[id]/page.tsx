"use client";

import Image from "next/image";
import Badge from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { useToast } from "@/context/toast-context";
import TrackingCard from "@/components/my-orders/tracking-card";

const OrderDetail = {
  id: "9292",
  date: "2025-03-10T14:34:00",
  status: "shipped",
  amount: 569.0,
  doctor: {
    name: "Dr. <PERSON>",
    title: "<PERSON><PERSON>, is a specialist in anti-aging medicine",
    image: "/icons/doctor-avatar-placeholder.svg",
  },
  prescriptionToken: "2DRPHCKTTGQKH4RMW7",
  prescriptionDate: "2024-09-11",
  prescriptionImage: "/images/dhl.jpg",
  treatments: [
    {
      name: "Domperidone Tablets 10mg",
      quantity: 50,
      instructions: "Once daily at night on an empty stomach for 5 days a week",
      price: 474,
      category: "Hair loss",
    },
    {
      name: "Epitalon 10mg/mL",
      quantity: 1,
      instructions: "Once daily at night on an empty stomach for 5 days a week",
      price: 237,
      category: "Anti-aging",
    },
  ],
};

const MyOrderDetailPage = () => {
  const { showToast } = useToast();

  const handleTrackClick = () => {
    // In a real application, this would redirect to the shipping company's tracking page
    window.open("https://www.dhl.com/global-en/home/<USER>", "_blank");
  };

  return (
    <main className="mt-16 flex min-h-screen w-full flex-col gap-4 bg-white px-4 py-4 sm:px-6 sm:py-6 md:mt-24 md:gap-8 lg:flex-row lg:px-16 lg:py-8">
      <div className="mx-auto w-full max-w-[1728px] flex-1">
        {/* Header Section */}
        <header className="relative">
          <div className="mb-2 flex flex-col gap-2 sm:mb-0 sm:flex-row sm:items-center">
            <h1 className="font-henju text-xl sm:text-2xl md:text-3xl">
              Order ID#{OrderDetail.id}
            </h1>
            <Badge variant="primary" withDot className="w-fit">
              {OrderDetail.status}
            </Badge>
          </div>
          <p className="text-md font-light text-neutral-600">
            {new Date(OrderDetail.date).toLocaleString()}
          </p>
          <p className="mt-2 font-medium text-black">${OrderDetail.amount.toFixed(2)}</p>
        </header>

        {/* Doctor Info */}
        <section className="flex items-center gap-3 py-4 sm:gap-4">
          <div className="flex size-8 items-center justify-center rounded-full bg-[#e9e9e9] sm:size-10">
            <Image
              src={OrderDetail.doctor.image}
              alt={OrderDetail.doctor.name}
              width={30}
              height={30}
              className="size-5 rounded-full sm:size-6"
            />
          </div>
          <div>
            <p className="text-md text-black sm:text-2md">{OrderDetail.doctor.name}</p>
            <p className="text-[10px] text-neutral-600 sm:text-md">{OrderDetail.doctor.title}</p>
          </div>
        </section>

        <div className="flex justify-start">
          <Button
            variant="primary"
            withArrow
            className="min-w-20 truncate py-1.5 pr-2 pl-3 text-md sm:min-w-24 sm:py-2 sm:text-2md lg:min-w-32 lg:pr-2 lg:pl-4"
          >
            View invoice
          </Button>
        </div>

        {/* Treatment Plan Section */}
        <section className="mt-6 w-full max-w-3xl sm:mt-8">
          <div className="px-0 py-3 sm:px-2 sm:py-4">
            <div className="flex flex-col gap-0">
              <p className="font-henju text-lg sm:text-xl">Treatment Plan</p>
            </div>

            {/* Desktop Table View (hidden on mobile) */}
            <div className="mt-4 hidden w-full space-y-4 sm:block">
              <table className="w-full">
                <thead className="w-full border-b border-[#f4feff] bg-white">
                  <tr>
                    <th className="px-4 py-2 text-left text-md font-semibold text-neutral-600">
                      Treatment
                    </th>
                    <th className="px-4 py-2 text-left text-md font-semibold text-neutral-600">
                      Dosing Instructions
                    </th>
                    <th className="px-4 py-2 text-center text-md font-semibold text-neutral-600">
                      Price
                    </th>
                  </tr>
                </thead>
                <tbody>
                  {OrderDetail.treatments.map((treatment, index) => (
                    <tr key={index} className="border-b border-[#f4feff]">
                      <td className="px-4 py-4">
                        <div className="flex items-start gap-2">
                          <div>
                            <p className="text-2md font-light">
                              {treatment.name} [{treatment.quantity}]
                            </p>
                            <Badge variant="pill" size="sm">
                              {treatment.category}
                            </Badge>
                          </div>
                        </div>
                      </td>
                      <td className="px-4 py-4 text-2md font-light">
                        <p className="w-60 text-md font-light">{treatment.instructions}</p>
                      </td>
                      <td className="px-4 py-4 text-center text-2md font-light">
                        ${treatment.price}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>

            {/* Mobile Card View (visible only on mobile) */}
            <div className="mt-3 w-full space-y-3 sm:hidden">
              {OrderDetail.treatments.map((treatment, index) => (
                <div
                  key={index}
                  className="rounded-lg border border-[#f4feff] bg-white p-3 shadow-sm"
                >
                  <div className="mb-2 flex items-start justify-between">
                    <div className="flex items-start gap-2">
                      <div>
                        <p className="text-md font-medium">
                          {treatment.name} [{treatment.quantity}]
                        </p>
                        <Badge variant="pill" size="sm" className="mt-1 text-[8px]">
                          {treatment.category}
                        </Badge>
                      </div>
                    </div>
                  </div>

                  <div className="mb-2">
                    <p className="mb-1 text-[10px] text-neutral-600">Dosing Instructions:</p>
                    <p className="text-[10px] font-light">{treatment.instructions}</p>
                  </div>

                  <div className="flex items-center justify-between">
                    <p className="text-md font-medium">${treatment.price}</p>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </section>
      </div>

      {/* Tracking Card */}
      <div className="mt-8 flex h-full w-full justify-center lg:mt-0 lg:w-auto lg:min-w-[300px] lg:justify-start xl:min-w-[400px]">
        <TrackingCard
          trackingNumber="456865"
          deliveryCompanyLogo="/images/dhl.png"
          onCopyClick={() => {
            navigator.clipboard.writeText("456865");
            showToast("Tracking number copied to clipboard", "success");
          }}
          onTrackClick={handleTrackClick}
          className="max-w-[400px] lg:max-w-none"
        />
      </div>
    </main>
  );
};

export default MyOrderDetailPage;
