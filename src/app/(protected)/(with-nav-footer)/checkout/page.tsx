"use client";

import Bag from "@/components/bag/bag";
import AddAddressDialog from "@/components/checkout/add-address-dialog";
import { But<PERSON> } from "@/components/ui/button";
import { PencilIcon, Plus, Trash2 } from "lucide-react";
import { useEffect, useState } from "react";
import { useToast } from "@/context/toast-context";
import ArrowRightIcon from "@/components/icons/arrow-right-icon";
import MCQCheckIcon from "@/components/icons/mcq-check-icon";
import Image from "next/image";
import { deleteAddress, useGetAddresses } from "@/api/address-service";
import TickCheckIcon from "@/components/icons/tick-check-icon";

const PAYMENT_METHODS = [
  {
    id: 1,
    name: "Debit, credit card",
    image: "/icons/payment-methods/debits.svg",
  },
  {
    id: 2,
    name: "Cash on delivery",
  },
  {
    id: 3,
    name: "Pay 4 interest-free payments of $85.72 Learn more",
    image: "/icons/payment-methods/afterpay.svg",
  },
  {
    id: 4,
    name: "Split into 4 payments of $85.72 Learn more",
    image: "/icons/payment-methods/zog.svg",
  },
];

const CheckoutPage = () => {
  const [isAddAddressDialogOpen, setIsAddAddressDialogOpen] = useState(false);
  const [addressToEdit, setAddressToEdit] = useState<string | undefined>(undefined);
  const [addressToDelete, setAddressToDelete] = useState<string | undefined>(undefined);
  const [isConfirmDeleteOpen, setIsConfirmDeleteOpen] = useState(false);
  const [selectedPaymentMethod, setSelectedPaymentMethod] = useState<number | null>(null);
  const [selectedAddressId, setSelectedAddressId] = useState<string | null>(null);
  const { addresses, revalidateAddresses, addressesLoading, addressesError } = useGetAddresses({
    page: 1,
    limit: 10,
  });
  const { showToast } = useToast();

  const handleAddNewAddress = () => {
    setAddressToEdit(undefined);
    setIsAddAddressDialogOpen(true);
  };
  useEffect(() => {
    if (addresses.length > 0 && !selectedAddressId) {
      setSelectedAddressId(addresses[0]._id);
    }
  }, [addresses, selectedAddressId]);

  const handleEditAddress = (addressId: string) => {
    setAddressToEdit(addressId);
    setIsAddAddressDialogOpen(true);
  };

  const handleCloseDialog = () => {
    setIsAddAddressDialogOpen(false);
    setAddressToEdit(undefined);
  };

  const handleDeleteAddress = (addressId: string) => {
    setAddressToDelete(addressId);
    setIsConfirmDeleteOpen(true);
  };
  const confirmDeleteAddress = async () => {
    if (addressToDelete) {
      try {
        await deleteAddress(addressToDelete);
        showToast("Address deleted successfully", "success");
        setAddressToDelete(undefined);
        setIsConfirmDeleteOpen(false);
      } catch (error) {
        console.error("Error deleting address:", error);
        showToast("Failed to delete address. Please try again.", "error");
      } finally {
        revalidateAddresses();
      }
    }
  };

  const cancelDeleteAddress = () => {
    setAddressToDelete(undefined);
    setIsConfirmDeleteOpen(false);
  };

  const handlePaymentMethodSelect = (methodId: number) => {
    setSelectedPaymentMethod(methodId);
  };

  const handlePaymentMethodKeyDown = (e: React.KeyboardEvent, methodId: number) => {
    if (e.key === "Enter" || e.key === " ") {
      e.preventDefault();
      handlePaymentMethodSelect(methodId);
    }
  };

  return (
    <main className="mt-16 flex min-h-screen w-full flex-col gap-4 bg-white px-4 py-4 sm:px-6 sm:py-6 md:mt-24 md:gap-8 lg:flex-row lg:px-16 lg:py-8">
      <div className="mx-auto max-w-[1728px] flex-1">
        <div className="mb-[23px] flex items-center justify-between">
          <h1 className="font-henju text-4xl sm:text-3xl">Select Address</h1>
        </div>

        {/* Address List */}
        <div className="space-y-4">
          {addresses.length === 0 ? (
            <div className="rounded-xl p-6 text-center">
              <div className="flex flex-col items-center justify-center rounded-xl p-8 text-center">
                <p className="mb-4 text-lg text-gray-700">
                  You don't have any saved addresses yet.
                </p>
                <Button
                  variant="outline"
                  onClick={handleAddNewAddress}
                  className="hover:border-[#23A1B0]hover:bg-white max-w-[325px] min-w-[250px] border-[#85B5A5] bg-white p-2 text-2md text-[#85B5A5] hover:scale-101"
                  withArrow
                  icon={<Plus size={16} />}
                >
                  Add New Address
                </Button>
              </div>
            </div>
          ) : (
            <>
              <div className="grid w-full justify-items-center gap-3 lg:[grid-template-columns:repeat(auto-fit,_minmax(250px,_0.2fr))]">
                {addresses.map((address) => (
                  <div
                    key={address._id}
                    onClick={() => setSelectedAddressId(address._id)}
                    className={`group relative w-full max-w-[325px] min-w-[250px] cursor-pointer rounded-xl border px-4 py-[8px] text-2md ${
                      selectedAddressId === address._id
                        ? "border-[#85B5A5] bg-[#F5F9F8]"
                        : "border-gray-200 bg-white"
                    }`}
                  >
                    <div className="absolute right-2 bottom-2 hidden items-center gap-2 group-hover:flex">
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleEditAddress(address._id);
                        }}
                        className="text-gray-500 hover:text-[#23A1B0]"
                      >
                        <PencilIcon size={16} />
                      </button>
                      <button
                        onClick={(e) => {
                          e.stopPropagation();
                          handleDeleteAddress(address._id);
                        }}
                        className="text-gray-500 hover:text-red-500"
                      >
                        <Trash2 size={16} />
                      </button>
                    </div>

                    <div className="flex items-start justify-between gap-2">
                      <div
                        className={`text-[15px] font-thin ${
                          selectedAddressId === address._id ? "text-black" : "text-[#535353]"
                        }`}
                      >
                        {address.street && <p>{address.street}</p>}
                        <p>
                          {address.city}, {address.state} {address.zipCode}
                        </p>
                        <p>{address.contactName}</p>
                      </div>

                      {selectedAddressId === address._id && (
                        <span className="text-[#23A1B0]">
                          <TickCheckIcon />
                        </span>
                      )}
                    </div>
                  </div>
                ))}

                <div className="flex items-center">
                  <Button
                    variant="outline"
                    onClick={handleAddNewAddress}
                    className="w-full max-w-[325px] min-w-[250px] truncate border-[#85B5A5] p-3 text-2md text-[#85B5A5] hover:scale-101 hover:border-[#23A1B0] hover:bg-white"
                    withArrow
                    icon={<Plus size={16} />}
                  >
                    Add New Address
                  </Button>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Add/Edit Address Dialog */}
        <AddAddressDialog
          isOpen={isAddAddressDialogOpen}
          onClose={handleCloseDialog}
          addressToEdit={addressToEdit}
        />

        {/* Delete Confirmation Dialog */}
        {isConfirmDeleteOpen && (
          <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/50">
            <div className="mx-4 w-full max-w-md rounded-xl bg-white p-6">
              <h2 className="font-henju mb-4 text-xl">Delete Address</h2>
              <p className="mb-6 text-gray-700">Are you sure you want to delete this address?</p>
              <div className="flex flex-col gap-3 sm:flex-row">
                <Button
                  type="button"
                  variant="outline"
                  className="font-henju w-full min-w-32 truncate border border-[#ffa068] bg-white py-2 pr-2 pl-5 text-2md text-[#ffa068] hover:bg-[#ffa068] hover:text-white"
                  onClick={cancelDeleteAddress}
                  withArrow
                  icon={<ArrowRightIcon size={16} color="#ffa068" />}
                >
                  Cancel
                </Button>
                <Button
                  type="button"
                  variant="outline"
                  className="font-henju w-full min-w-32 truncate border border-[#873A3A] bg-[#FFE2E2] py-2 pr-2 pl-5 text-2md text-[#873A3A] hover:bg-[#873A3A] hover:text-black"
                  onClick={confirmDeleteAddress}
                  withArrow
                  icon={<Trash2 size={16} />}
                >
                  Delete
                </Button>
              </div>
            </div>
          </div>
        )}
        {/* Payment Methods */}
        <div>
          <h1 className="font-henju mt-8 text-2xl sm:text-3xl">Select payment method</h1>
          <div className="mt-4 space-y-4">
            {PAYMENT_METHODS.map((method) => (
              <div
                key={method.id}
                className={`focus:ring-opacity-50 flex cursor-pointer items-center justify-between gap-8 rounded-xl p-3 transition-all duration-200 focus:outline-none`}
                onClick={() => handlePaymentMethodSelect(method.id)}
                onKeyDown={(e) => handlePaymentMethodKeyDown(e, method.id)}
                role="radio"
                aria-checked={selectedPaymentMethod === method.id}
                tabIndex={0}
              >
                <div className="flex items-center gap-2">
                  <div
                    className={`size-6 ${selectedPaymentMethod === method.id ? "bg-primary" : "border border-[#EDEDED] bg-white"} relative overflow-hidden rounded-[8px]`}
                  >
                    {selectedPaymentMethod === method.id && (
                      <div className="absolute top-1 left-1">
                        <MCQCheckIcon />
                      </div>
                    )}
                  </div>
                  <p className="text-2md font-light">{method.name}</p>
                </div>
                {method.image && (
                  <Image
                    src={method.image}
                    alt={method.name}
                    className="h-6 w-6"
                    width={24}
                    height={24}
                  />
                )}
              </div>
            ))}
          </div>
        </div>
      </div>

      <div className="h-full rounded-3xl bg-white pb-4 shadow-[0px_0px_15px_0px_rgba(0,0,0,0.08)] md:min-w-[463px]">
        <Bag />
      </div>
    </main>
  );
};

export default CheckoutPage;
