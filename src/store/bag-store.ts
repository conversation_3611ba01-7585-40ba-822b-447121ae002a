'use client';

import { addBagItem, clearBag as clearBagApi, deleteBagItem, updateBagItem } from '@/api/bag-service';
import { BagState, Discount, IBagItem } from '@/types/bag';
import { useAuthStore } from '@/store/auth-store';
import { create } from 'zustand';
import { createJSONStorage, persist } from 'zustand/middleware';

interface BagStore extends BagState {
  // Actions
  setBagItems: (items: IBagItem[]) => void;
  addItem: (item: IBagItem) => Promise<void>;
  updateItemQuantity: (itemId: string, quantity: number) => Promise<void>;
  applyDiscount: (code: string) => boolean;
  removeDiscount: () => void;
  setDiscountCode: (code: string) => void;
  getSubtotal: () => number;
  getDiscountAmount: () => number;
  getTotal: () => number;
  clearBag: () => Promise<void>;
  removeItem: (itemId: string) => Promise<void>;

  // API Status
  isLoading: boolean;
  error: string | null;
  isDeletingItem: boolean;
  isClearingBag: boolean;
}

// Mock discount codes for demo purposes
const DISCOUNT_CODES: Record<string, Discount> = {
  'MVP20': { code: 'MVP20', amount: 20, type: 'fixed' },
  'WELCOME10': { code: 'WELCOME10', amount: 10, type: 'percentage' },
};

// Helper function to check if user is authenticated
const isUserAuthenticated = (): boolean => {
  // Access auth store directly (not using hook since this is outside React component)
  let isAuthenticated = false;

  if (typeof window !== 'undefined') {
    const authState = localStorage.getItem('auth-storage');
    if (authState) {
      try {
        const { state } = JSON.parse(authState);
        isAuthenticated = state?.isAuthenticated || false;
      } catch (error) {
        console.error('Error parsing auth state:', error);
      }
    }
  }

  return isAuthenticated;
};

// Local storage operations for unauthenticated users
const localStorageOperations = {
  // Generate a unique ID for local cart items
  generateLocalItemId: (): string => {
    return `local_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`;
  },

  // Add item to local storage
  addItem: (item: IBagItem): IBagItem => {
    // Ensure the item has an ID
    const itemWithId = {
      ...item,
      _id: item._id || localStorageOperations.generateLocalItemId()
    };

    return itemWithId;
  },

  // Update item in local storage
  updateItem: (itemId: string, updates: Partial<IBagItem>, items: IBagItem[]): IBagItem[] => {
    return items.map(item =>
      item._id === itemId ? { ...item, ...updates } : item
    );
  },

  // Remove item from local storage
  removeItem: (itemId: string, items: IBagItem[]): IBagItem[] => {
    return items.filter(item => item._id !== itemId);
  }
};

export const useBagStore = create<BagStore>()(
  persist(
    (set, get) => ({
      // Initial state
      items: [],
      discount: null,
      discountCode: '',
      deliveryFee: 10,
      isLoading: false,
      error: null,
      isDeletingItem: false,
      isClearingBag: false,

      setBagItems: (items: IBagItem[]) => {
        set({ items });
      },

      // Actions
      addItem: async (item: IBagItem) => {
        set({ isLoading: true, error: null });
        try {
          // Get the latest state
          const { items } = get();
          const existingItem = items.find((i) =>
            i.productId?._id === item.productId?._id
          );

          // Check if user is authenticated
          if (isUserAuthenticated()) {
            // Add to API for authenticated users
            await addBagItem({
              productId: item.productId?._id,
              quantity: item.quantity || 1,
              isPrescription: item.isPrescription,
              prescriptionImage: item.prescriptionImage,
              bookinId: item.bookinId,
              prescribedByDoctor: item.prescribedByDoctor
            });
          }

          // Update local state (for both authenticated and unauthenticated users)
          if (existingItem) {
            // If item already exists, update quantity
            set({
              items: items.map((i) =>
                i.productId?._id === item.productId?._id
                  ? { ...i, quantity: (i.quantity || 1) + (item.quantity || 1) }
                  : i
              ),
              isLoading: false
            });
          } else {
            // For unauthenticated users, ensure the item has a local ID
            const newItem = isUserAuthenticated()
              ? { ...item, quantity: item.quantity || 1 }
              : localStorageOperations.addItem({ ...item, quantity: item.quantity || 1 });

            // Add new item with quantity
            set({
              items: [...items, newItem],
              isLoading: false
            });
          }
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to add item to bag',
            isLoading: false
          });
        }
      },

      removeItem: async (itemId: string) => {
        set({ isLoading: true, error: null, isDeletingItem: true });
        try {
          // Check if user is authenticated
          if (isUserAuthenticated() && !itemId.startsWith('local_')) {
            // Remove from API only for authenticated users and non-local items
            await deleteBagItem(itemId);
          }

          // Update local state for both authenticated and unauthenticated users
          const { items } = get();
          set({
            items: localStorageOperations.removeItem(itemId, items),
            isLoading: false,
            isDeletingItem: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to remove item from bag',
            isLoading: false,
            isDeletingItem: false
          });
        }
      },

      updateItemQuantity: async (itemId: string, quantity: number) => {
        set({ isLoading: true, error: null });
        try {
          // Find the item to get its data
          const { items } = get();
          const item = items.find((item) => item._id === itemId);

          if (!item) {
            throw new Error('Item not found');
          }

          // Check if user is authenticated and item is not local
          if (isUserAuthenticated() && !itemId.startsWith('local_')) {
            // Update in API only for authenticated users and non-local items
            await updateBagItem({ quantity }, itemId);
          }

          // Update local state for both authenticated and unauthenticated users
          set({
            items: localStorageOperations.updateItem(itemId, { quantity }, items),
            isLoading: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to update item quantity',
            isLoading: false
          });
        }
      },

      clearBag: async () => {
        set({ isLoading: true, error: null, isClearingBag: true });
        try {
          // Check if user is authenticated
          if (isUserAuthenticated()) {
            // Clear in API only for authenticated users
            await clearBagApi();
          }

          // Update local state for both authenticated and unauthenticated users
          set({
            items: [],
            discount: null,
            discountCode: '',
            isLoading: false,
            isClearingBag: false
          });
        } catch (error) {
          set({
            error: error instanceof Error ? error.message : 'Failed to clear bag',
            isLoading: false,
            isClearingBag: false
          });
        }
      },

      applyDiscount: (code: string): boolean => {
        const discount = DISCOUNT_CODES[code];
        if (discount) {
          set({
            discount,
            discountCode: code,
          });
          return true;
        }
        return false;
      },

      removeDiscount: () => {
        set({
          discount: null,
          discountCode: '',
        });
      },

      setDiscountCode: (code: string) => {
        set({
          discountCode: code,
        });
      },

      getSubtotal: () => {
        const { items } = get();
        return items.reduce((total, item) => {
          // Use productId.price for IBagItem
          return total + (item.productId?.price || 0) * (item.quantity || 1);
        }, 0);
      },

      getDiscountAmount: () => {
        const { discount } = get();
        const subtotal = get().getSubtotal();

        if (!discount) return 0;

        if (discount.type === 'percentage') {
          return (subtotal * discount.amount) / 100;
        } else {
          return discount.amount;
        }
      },

      getTotal: () => {
        const subtotal = get().getSubtotal();
        const discountAmount = get().getDiscountAmount();
        const { deliveryFee } = get();

        return subtotal - discountAmount + deliveryFee;
      },
    }),
    {
      name: 'bag-storage',
      storage: createJSONStorage(() => localStorage),
    }
  )
);
