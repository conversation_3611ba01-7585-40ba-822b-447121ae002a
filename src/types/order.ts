export interface CreateOrderData {
    deliveryAddress: string;
    paymentMethod: string;
    notes?: string;
}


export interface OrderItem {
    name: string;
    quantity: number;
}

`
{
    "_id": "686e20ab0e007e6de01deed3",
    "user": "67f38ab49bcb60e9a297d97c",
    "products": [
        {
            "product": {
                "_id": "680253a4ee29c12843da0823",
                "name": "Glow Booster ",
                "main_image": "https://cdn.faisalkc.com/storage/cdn/wkCf5JlkIXHzMG95oA424eSwtep2jyuBhBeywE2w.png",
                "price": 755,
                "minOrderQuantity": 1,
                "maxOrderQuantity": 10,
                "stock": 0,
                "slug": "12"
            },
            "quantity": 1,
            "unitPrice": 755,
            "totalPrice": 755,
            "_id": "686e20ab0e007e6de01deed4"
        },
        {
            "product": {
                "_id": "6805e50cee29c12843da0b57",
                "name": "name 1 concentration1",
                "main_image": "https://cdn.faisalkc.com/storage/cdn/jJPlAIOKTdbC6j95BiNhH3zLABi6MlzgVwQeG7au.png",
                "price": 100,
                "minOrderQuantity": 1,
                "maxOrderQuantity": 20,
                "stock": 199,
                "slug": "test-500"
            },
            "quantity": 1,
            "unitPrice": 100,
            "totalPrice": 100,
            "_id": "686e20ab0e007e6de01deed5"
        },
        {
            "product": {
                "_id": "68024760ee29c12843da06d1",
                "name": "Glow Booster",
                "main_image": "https://cdn.faisalkc.com/storage/cdn/h8iNcaYrVtkpHIl0xROo9KvdTH2j1DruW9JDcmQJ.png",
                "price": 75,
                "minOrderQuantity": 1,
                "maxOrderQuantity": 10,
                "stock": 9,
                "slug": "12"
            },
            "quantity": 1,
            "unitPrice": 75,
            "totalPrice": 75,
            "_id": "686e20ab0e007e6de01deed6"
        }
    ],
    "deliveryAddress": "6824785846b5f1bf6458d6bb",
    "orderReference": "OR-090725-7378",
    "totalPrice": 930,
    "subTotal": 930,
    "discountPrice": 0,
    "taxAmount": 0,
    "additionalCharges": 0,
    "paymentStatus": "pending",
    "status": "placed",
    "fulfillment": {
        "status": "pending",
        "shippingCost": 0,
        "_id": "686e20ab0e007e6de01deed7"
    },
    "createdAt": "2025-07-09T07:56:27.784Z",
    "updatedAt": "2025-07-09T07:56:27.784Z",
    "__v": 0,
    "userDetails": [
        {
            "_id": "67f38ab49bcb60e9a297d97c",
            "email": "<EMAIL>",
            "name": "aruna nuwantha 6"
        }
    ]
}
`

export interface Order {
    additionalCharges: number;
    deliveryAddress: string;
    discountPrice: number;
    fulfillment: {
        shippingCost: number;
        shippingMethod: string;
    }
}
