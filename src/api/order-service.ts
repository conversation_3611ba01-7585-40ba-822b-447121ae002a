import { fetcher, revolvedCreator } from "@/lib/axios";
import { API_ENDPOINTS } from "./api-endpoints";
import { CreateOrderData } from "@/types/order";
import useSWR, { mutate, SWRConfiguration } from "swr";
import { useMemo } from "react";

const swrOptions: SWRConfiguration = {
    // revalidateIfStale: false,
    revalidateOnFocus: false,
    // revalidateOnReconnect: false,
};

interface GetOrderListParams {
    limit?: number;
    searchQuery?: string;
    page?: number;
}

export function useGetOrderList({
    page,
    limit,
    searchQuery = "",
}: GetOrderListParams) {
    const getTheFullUrl = () => {
        const queryParams: { [key: string]: any } = {
            page: page + 1, // API uses 1-based indexing
            limit,
        };

        if (searchQuery !== "") queryParams.search = searchQuery;

        return `${API_ENDPOINTS.order.getList}?${new URLSearchParams(queryParams)}`;
    };

    const { data, isLoading, error, isValidating } = useSWR<any>(
        getTheFullUrl,
        fetcher,
        swrOptions
    );

    const memoizedValue = useMemo(
        () => ({
            orders: data?.data?.data || [],
            ordersLoading: isLoading,
            ordersError: error,
            ordersValidating: isValidating,
            ordersEmpty: !isLoading && !isValidating && !data?.data?.data?.length,
            totalCount: data?.data?.totalCount ?? 0,
            totalPages: data?.data?.totalPages ?? 0,
        }),
        [data?.data, error, isLoading, isValidating]
    );

    const revalidateOrdersList = () => {
        mutate(getTheFullUrl());
    };

    return {
        ...memoizedValue,
        revalidateOrdersList,
    };
}

export function createOrder(orderData: CreateOrderData) {
    const url = API_ENDPOINTS.order.create;
    const result = revolvedCreator([url, orderData]);
    return result;
}