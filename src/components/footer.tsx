"use client";
import { useGetPublicCategories } from "@/api/category-service";
import { ChevronDown } from "lucide-react";
import Image from "next/image";
import Link from "next/link";
import React, { useState } from "react";

const Footer = () => {
  const { categories } = useGetPublicCategories({ limit: 100, page: 0 });
  const FOOTER_LINKS = [
    {
      id: 1,
      label: "Treatments",
      links: [
        ...categories.map((category) => ({
          label: category.name,
          href: `/treatments/${category.slug}`,

          subItems:
            category.slug === "skincare"
              ? [
                  { label: "Acne", href: `/treatments/${category.slug}?subcategory=acne` },
                  {
                    label: "Wrinkles & Aging",
                    href: `/treatments/${category.slug}?subcategory=wrinkles-aging`,
                  },
                  {
                    label: "Pigmentation",
                    href: `/treatments/${category.slug}?subcategory=pingmentation`,
                  },
                  {
                    label: "Excessive Sweating",
                    href: `/treatments/${category.slug}?subcategory=excessive-sweating`,
                  },
                ]
              : [],
        })),
      ],
    },
    {
      id: 2,
      label: "About",
      links: [
        { label: "Our Approach", href: "/our-approach" },
        { label: "How it works", href: "/how-it-works" },
        { label: "Everyday Essentials", href: "/shop" },
        { label: "Wellness Academy", href: "/wellness-academy" },
        { label: "Careers", href: "/careers" },
      ],
    },
    {
      id: 3,
      label: "Toolkit",
      links: [
        { label: "Biological Age Calculator", href: "/biological-age-calculator" },
        { label: "BMI Calculator", href: "/bmi-calculator" },
        { label: "Energy Expenditure(TDEE) Calculator", href: "/tdee-calculator" },
        { label: "Calorie Deficit Calculator", href: "/calorie-deficit-calculator" },
        { label: "Protein Calculator", href: "/protein-calculator" },
      ],
    },
    {
      id: 4,
      label: "Help Center",
      links: [
        { label: "Pricing", href: "/pricing" },
        { label: "Support", href: "/support" },
        { label: "FAQs", href: "/faqs" },
      ],
    },
  ];

  const SOCIAL_LINKS = [
    {
      label: "Instagram",
      href: "https://www.instagram.com/revolvedhealth/",
      image: "/icons/social/instagram.svg",
    },
    {
      label: "LinkedIn",
      href: "https://www.linkedin.com/company/revolvedhealth/",
      image: "/icons/social/linkedin.svg",
    },
    {
      label: "Facebook",
      href: "https://www.facebook.com/revolvedhealth",
      image: "/icons/social/facebook.svg",
    },

    { label: "X", href: "https://x.com/revolvedhealth", image: "/icons/social/twitter.svg" },
    {
      label: "Tiktok",
      href: "https://www.tiktok.com/@revolvedhealth",
      image: "/icons/social/tiktok.svg",
    },
    {
      label: "Youtube",
      href: "https://www.youtube.com/@revolvedhealth",
      image: "/icons/social/youtube.svg",
    },
  ];

  const PAYMENT_METHODS = [
    "/icons/payment-methods/epay-white.svg",
    "/icons/payment-methods/visa-white.svg",
    "/icons/payment-methods/amex-white.svg",
    "/icons/payment-methods/mastercard-white.svg",
  ];

  const [showSubItems, setShowSubItems] = useState<boolean>(false);

  const renderLinkSection = (section: any) => {
    return (
      <div key={section.id} className="font-quinn">
        <h3 className="font-henju mb-4 text-xl font-normal text-[#8ce8c3]">{section.label}</h3>
        <nav className="space-y-4">
          {section.links.map((link, index) => (
            <React.Fragment key={index}>
              {!link.subItems ? (
                <Link
                  href={link.href}
                  className="text-2md block font-light text-white transition-colors duration-200 hover:text-[#8ce8c3]"
                >
                  {link.label}
                </Link>
              ) : (
                <button
                  className="text-2md flex items-center font-light text-white transition-all duration-200 hover:text-[#8ce8c3]"
                  onClick={() => {
                    setShowSubItems(!showSubItems);
                  }}
                >
                  {link.label}
                </button>
              )}

              {link.subItems && (
                <div className={`mt-2 space-y-3 pl-4`}>
                  {link.subItems.map((subItem, subIndex) => (
                    <Link
                      key={subIndex}
                      href={subItem.href}
                      className="text-md block text-white/80 transition-colors duration-200 hover:text-[#8ce8c3]"
                    >
                      {subItem.label}
                    </Link>
                  ))}
                </div>
              )}
            </React.Fragment>
          ))}
        </nav>
      </div>
    );
  };

  return (
    <footer className="relative z-1 mt-20 w-full bg-[#0c7885]">
      {/* Background Pattern */}
      <div className="absolute right-0 bottom-5 -z-10 hidden lg:block" id="footer">
        <Image
          src="/icons/footer-background-pattern.svg"
          alt="Footer background pattern"
          width={400}
          height={400}
          priority
          className="object-contain"
        />
      </div>

      <div className="mx-auto w-full px-12 pt-12 pb-20 md:px-24 md:pt-20 md:pb-16">
        <div className="grid grid-cols-1 gap-2 md:grid-cols-2 lg:grid-cols-12">
          {/* Logo and Address Section */}
          <div className="lg:col-span-4">
            <div className="relative h-[60px] w-[300px]">
              <Image
                src="/icons/logo-white.svg"
                alt="Revolved Logo Icon"
                priority
                width={280}
                height={48}
              />
            </div>
            <div className="text-2md mt-8 flex gap-2 px-2 font-light text-black md:px-4">
              {SOCIAL_LINKS.map((link, index) => (
                <a
                  key={index}
                  href={link.href}
                  className="relative mb-4 inline-block h-5 w-5"
                  target="_blank"
                  rel="noopener noreferrer"
                >
                  <Image
                    src={link.image}
                    alt={link.label}
                    width={20}
                    height={20}
                    priority
                    className="h-5 w-5 object-contain"
                  />
                </a>
              ))}
            </div>
          </div>

          {/* Navigation Sections */}
          {/* Treatments */}
          <div className="grid grid-cols-1 gap-8 sm:grid-cols-2 lg:col-span-8 lg:grid-cols-3">
            <div className="">{renderLinkSection(FOOTER_LINKS[0])}</div>
            <div className="gap-8 space-y-8">
              {FOOTER_LINKS.slice(1, 3).map((section) => renderLinkSection(section))}
            </div>
            <div>{renderLinkSection(FOOTER_LINKS[3])}</div>
          </div>
        </div>
      </div>

      {/* Bottom Bar */}
      <div className="absolute bottom-0 left-0 flex h-16 w-full flex-col bg-[#0c5159] py-2 md:h-8 md:flex-row">
        <div className="container mx-auto px-4">
          <nav className="md:text-md flex flex-wrap items-baseline justify-center gap-x-1 gap-y-1 text-xs font-light text-white">
            <Link href="#" className="truncate hover:underline">
              Privacy Policy
            </Link>
            <span className="truncate text-white">·</span>
            <Link href="#" className="truncate hover:underline">
              Terms & Conditions
            </Link>
            <span className="truncate text-white">·</span>
            <Link href="#" className="truncate hover:underline">
              Fulfilment, Shipping & Returns Policy
            </Link>
          </nav>
        </div>
        <div className="mx-auto flex h-8 items-center justify-center px-2 md:absolute md:right-1 md:bottom-0">
          {PAYMENT_METHODS.map((method, index) => (
            <Image
              key={index}
              src={method}
              alt={`Payment method ${index + 1}`}
              width={20}
              height={20}
              sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
              priority
              className="inline-block h-4 w-6"
            />
          ))}
        </div>
      </div>
    </footer>
  );
};

export default Footer;
