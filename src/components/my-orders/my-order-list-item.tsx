import { motion } from "framer-motion";
import Link from "next/link";
import Badge from "../ui/badge";
import { Copy } from "lucide-react";
import Image from "next/image";
import { cn } from "@/lib/utils";
import { useToast } from "@/context/toast-context";
import ArrowRightIcon from "../icons/arrow-right-icon";

interface OrderItem {
  name: string;
  quantity: number;
}

interface Order {
  id: string;
  date: string;
  status: string;
  amount: number;
  items: OrderItem[];
}

interface MyOrderListItemProps {
  order: Order;
  index: number;
}

const MyOrderListItem = ({ order, index }: MyOrderListItemProps) => {
  const { showToast } = useToast();

  const handleCopyTracking = () => {
    navigator.clipboard.writeText("456865");
    showToast("Tracking number copied to clipboard", "success");
  };

  const handleTrackOrder = () => {
    window.open("https://www.dhl.com/global-en/home/<USER>", "_blank");
  };

  return (
    <motion.div
      key={order.id}
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ delay: index * 0.1 }}
      className="overflow-hidden rounded-2xl bg-white shadow-[0px_4px_20px_0px_rgba(0,0,0,0.06)]"
    >
      {/* Order Header */}
      <div className="border-b border-[#e9e9e9] p-4 sm:p-5 md:p-6">
        <Link href={`/my-orders/${order.id}`} className="block">
          {/* Mobile Status Bar - Only visible on mobile */}
          <div className="mb-3 flex items-center justify-between md:hidden">
            <Badge variant="warning" size="sm" className="capitalize" withDot>
              {order.status}
            </Badge>
            <p className="text-2md font-semibold">${order.amount.toFixed(2)}</p>
          </div>

          {/* Main Content - Responsive Layout */}
          <div className="flex flex-col gap-4 md:flex-row md:items-start md:justify-between">
            {/* Left Column - Order Details */}
            <div className="w-full md:w-auto">
              <h3 className="font-henju text-xl sm:text-2xl">Order ID#{order.id}</h3>
              <p className="text-md font-light text-neutral-600 sm:text-2md">
                {new Date(order.date).toLocaleString()}
              </p>
              <div className="mt-3 sm:mt-4">
                <p className="font-henju text-lg text-black sm:text-xl">Treatments</p>
                <ul className="ml-2 space-y-1">
                  {order.items.map((item, idx) => (
                    <li
                      key={idx}
                      className="flex items-center gap-1.5 text-md font-light sm:text-2md"
                    >
                      <span className="flex-shrink-0">
                        <svg
                          width="5"
                          height="5"
                          viewBox="0 0 8 8"
                          fill="none"
                          xmlns="http://www.w3.org/2000/svg"
                        >
                          <circle cx="4" cy="4" r="3" fill="#000" />
                        </svg>
                      </span>
                      <span className="line-clamp-1">
                        {item.name} [{item.quantity}]
                      </span>
                    </li>
                  ))}
                </ul>
              </div>
            </div>

            {/* Right Column - Status & Actions */}
            <div
              className={cn(
                "flex flex-col items-start justify-between gap-3 sm:flex-row sm:items-center sm:gap-2 md:flex-col md:items-end md:justify-end",
                "mt-4 border-t border-[#e9e9e9] pt-4 md:mt-0 md:border-t-0 md:pt-0"
              )}
            >
              {/* Status & Price - Hidden on mobile, visible on larger screens */}
              <div className="mb-1 hidden md:block">
                <Badge variant="warning" size="sm" className="capitalize" withDot>
                  {order.status}
                </Badge>
              </div>
              <div className="mb-1 hidden md:block">
                <p className="text-2md font-semibold">${order.amount.toFixed(2)}</p>
              </div>

              {/* Tracking Info & Delivery Info Container */}
              <div className="flex w-full flex-col items-end sm:w-auto">
                {/* Tracking Info */}
                <div className="mb-2 flex items-center justify-end gap-1">
                  <p className="text-md font-light text-black sm:text-2md">Tracking#456865</p>
                  <button
                    className="rounded-full p-1 transition-colors hover:bg-gray-100"
                    onClick={handleCopyTracking}
                    aria-label="Copy tracking number"
                  >
                    <Copy className="size-3 sm:size-4" />
                  </button>
                </div>

                {/* Delivery Info */}
                <div className="mb-3 flex flex-col items-start sm:mb-0">
                  <p className="text-[10px] font-light text-gray-600 sm:text-[9px]">Delivered By</p>
                  <div className="flex-shrink-0">
                    <Image
                      src={"/images/dhl.png"}
                      alt="DHL"
                      width={60}
                      height={30}
                      className="h-auto w-12 object-contain sm:w-16 md:w-24"
                    />
                  </div>
                </div>
              </div>

              {/* Track Order Button */}
              <button
                onClick={handleTrackOrder}
                className="flex items-center justify-center gap-2 self-start rounded-full px-3 py-1.5 outline outline-offset-[-1px] outline-[#85b5a5] transition-colors hover:bg-[#85b5a5]/5 sm:self-end sm:px-4 sm:py-2"
                aria-label="Track order"
              >
                <p className="text-[10px] whitespace-nowrap text-[#85b5a5] sm:text-md">
                  Track Order
                </p>
                <div className="flex items-center justify-center">
                  <ArrowRightIcon color="#85b5a5" size={15} />
                </div>
              </button>
            </div>
          </div>
        </Link>
      </div>
    </motion.div>
  );
};

export default MyOrderListItem;
