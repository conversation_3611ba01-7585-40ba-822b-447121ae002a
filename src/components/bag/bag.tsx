"use client";

import { getBagItemList } from "@/api/bag-service";
import { useToast } from "@/context/toast-context";
import { cn } from "@/lib/utils";
import { useBagStore } from "@/store/bag-store";
import { AnimatePresence, motion } from "framer-motion";
import { Minus, X } from "lucide-react";
import Image from "next/image";
import { useRouter } from "next/navigation";
import React, { useState } from "react";
import { Button } from "../ui/button";
import BackToHomeButton from "./back-to-home-button";
import Switch from "../ui/switch";

interface BagProps {
  className?: string;
  onClose?: () => void;
  isModal?: boolean;
}

const Bag: React.FC<BagProps> = ({ className, onClose, isModal = false }) => {
  const router = useRouter();
  const {
    items,
    discount,
    deliveryFee,
    applyDiscount,
    removeDiscount,
    getSubtotal,
    getDiscountAmount,
    getTotal,
    clearBag,
    removeItem,
    isDeletingItem,
    isClearingBag,
  } = useBagStore();

  // Use the optimized getBagItemList function with useMemo
  const {
    bagItems,
    bagItemsLoading: isLoading,
    bagItemsError: error,
    revalidateBagItemList,
  } = getBagItemList();

  const { showToast } = useToast();
  const [code, setCode] = useState("");
  const [isSubscribed, setIsSubscribed] = useState(false);

  const handleApplyDiscount = () => {
    if (!code.trim()) {
      showToast("Please enter a discount code", "error");
      return;
    }

    if (applyDiscount(code.trim())) {
      showToast(`Discount code ${code} applied successfully!`, "success");
      setCode("");
    } else {
      showToast(`Invalid discount code: ${code}`, "error");
    }
  };

  const handleRemoveDiscount = () => {
    removeDiscount();
    showToast("Discount code removed", "info");
  };

  const handleRemoveItem = async (itemId: string) => {
    if (!itemId) return;
    try {
      await removeItem(itemId);
      showToast("Item removed from bag", "info");
    } catch (error) {
      showToast("Failed to remove item", "error");
    }
  };

  const handleClearBag = async () => {
    try {
      await clearBag();
      showToast("Bag cleared", "info");
    } catch (error) {
      showToast("Failed to clear bag", "error");
    }
  };

  const handleCheckout = () => {
    if (items.length === 0) {
      showToast("Your bag is empty", "error");
      return;
    }
    router.push("/checkout");
    showToast("Proceeding to checkout...", "info");
    // Implement checkout logic here
  };

  // Format currency
  const formatCurrency = (amount: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
    }).format(amount);
  };

  const handleSubscriptionToggle = () => {
    setIsSubscribed((prev) => !prev);
  };

  const subtotal = getSubtotal();
  const discountAmount = getDiscountAmount();
  const total = getTotal();

  return (
    <div
      className={cn(
        "font-quinn w-full max-w-full px-4 py-4 transition-all duration-300 md:max-w-[463px] md:px-8 2xl:max-w-[500px]",
        isModal ? "h-full overflow-y-auto" : "",
        className
      )}
    >
      <div className="mx-auto w-full">
        {/* Header */}
        <div className="mb-6 hidden items-center justify-between md:flex">
          <h2 className="font-henju text-xl font-normal text-black">My bag</h2>
          {isModal && onClose && (
            <button
              onClick={onClose}
              className="rounded-full p-2 text-gray-500 transition-colors hover:bg-gray-100 hover:text-gray-700"
              aria-label="Close bag"
            >
              <svg
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
                xmlns="http://www.w3.org/2000/svg"
                className="size-5 sm:size-6"
              >
                <path
                  d="M18 6L6 18M6 6L18 18"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
            </button>
          )}
        </div>

        {/* Main Container */}
        {isLoading ? (
          <div className="flex flex-col items-center justify-center gap-4 py-8 text-center md:gap-8">
            <p className="text-lg font-normal text-black sm:text-xl">Loading your bag...</p>
          </div>
        ) : error ? (
          <div className="flex flex-col items-center justify-center gap-4 py-8 text-center md:gap-8">
            <p className="text-lg font-normal text-red-500 sm:text-xl">Error loading your bag</p>
            <Button onClick={() => revalidateBagItemList()} variant="primary">
              Try Again
            </Button>
          </div>
        ) : items.length === 0 ? (
          <div className="flex flex-col items-center justify-center gap-4 py-8 text-center md:gap-8">
            <div className="flex size-36 items-center justify-center rounded-full bg-[#F3F8F6] sm:size-48 md:size-56">
              <Image
                src="/icons/empty-bag.svg"
                alt="Empty bag"
                width={80}
                height={80}
                className="h-16 w-16 sm:h-20 sm:w-20 md:h-24 md:w-24"
              />
            </div>
            <p className="text-lg font-normal text-black sm:text-xl">{`Your bag is empty :(`}</p>
            <BackToHomeButton />
          </div>
        ) : (
          <div className="rounded-2xl bg-white">
            {/* Items */}
            <div className="space-y-4">
              <div className="max-h-52 space-y-4 overflow-x-hidden overflow-y-auto md:max-h-64">
                <AnimatePresence>
                  {items.map((item, index) => (
                    <motion.div
                      key={item.productId?._id || ""}
                      initial={{ opacity: 0, y: 20 }}
                      animate={{ opacity: 1, y: 0 }}
                      exit={{ opacity: 0, x: -20 }}
                      transition={{ duration: 0.2 }}
                      className={`flex items-start gap-4 ${index === items.length - 1 ? "pb-0" : "border-b border-gray-200 pb-4"}`}
                    >
                      <button
                        className="flex size-6 items-center justify-center rounded-full bg-[#ffe2e2] transition-all duration-200 hover:scale-105 hover:bg-[#ffcaca] active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#EDEDED]"
                        onClick={() => handleRemoveItem(item._id || "")}
                        disabled={isDeletingItem}
                        aria-label="Remove item"
                      >
                        {isDeletingItem ? (
                          <span className="size-4 animate-spin rounded-full border-2 border-[#873A3A] border-t-transparent" />
                        ) : (
                          <Minus className="size-4 stroke-[#873A3A] stroke-1 disabled:stroke-[#999999]" />
                        )}
                      </button>

                      <div className="flex-1">
                        <div className="flex gap-1">
                          <div className="flex size-8 items-center justify-center rounded-full bg-[#D5ECE3]">
                            <Image
                              src={item.productId?.main_image || "/images/placeholder.svg"}
                              alt={item.productId?.name || "Product Image"}
                              width={100}
                              height={100}
                              className="size-7 rounded-full object-cover"
                            />
                          </div>
                          <div className="flex w-full justify-between">
                            <div>
                              <h3 className="text-2md font-light text-black">
                                {item.productId?.name || "Product"}
                              </h3>
                              <p className="line-clamp-1 max-w-sm text-sm font-light text-gray-500">
                                {item.productId?.description || "Description"}
                              </p>
                            </div>
                            <span className="font-dm-sans text-2md font-normal text-black">
                              {formatCurrency((item.productId?.price || 0) * (item.quantity || 1))}
                            </span>
                          </div>
                        </div>
                        <div className="mt-1 flex items-center gap-4">
                          <span className="inline-block rounded-full bg-[#f3edfa] px-2 py-[0.5px] text-[10px] font-normal text-[#82618e] outline outline-[#e7d9ef]">
                            {"Category"}
                          </span>
                          {item.quantity && item.quantity > 1 && (
                            <div className="text-md font-normal text-gray-500">
                              Qty: {item.quantity}
                            </div>
                          )}
                        </div>
                        <div className="mt-2 flex items-center justify-between">
                          <div>
                            <h4 className="text-sm leading-[1.2] text-black">Subscription</h4>
                            <p className="text-xs font-light text-gray-500">
                              Refill every 3 months
                            </p>
                          </div>
                          <Switch
                            checked={!isSubscribed}
                            onCheckedChange={handleSubscriptionToggle}
                            className={`${"data-[state=checked]:bg-[#4fa097] data-[state=unchecked]:bg-[#787880]/20"} `}
                          />
                        </div>
                      </div>
                    </motion.div>
                  ))}
                </AnimatePresence>
                {/* Clear Bag Button */}
                <div className="flex justify-end">
                  <button
                    onClick={handleClearBag}
                    disabled={isClearingBag}
                    className="text-md font-light text-[#c13333] disabled:opacity-50"
                  >
                    {isClearingBag ? "Clearing..." : "Clear"}
                  </button>
                </div>
              </div>
              {/* Discount Code Section */}
              <div className="mt-8 flex flex-col gap-4 sm:flex-row">
                <input
                  type="text"
                  placeholder="Discount code"
                  value={code}
                  onChange={(e) => setCode(e.target.value)}
                  className="text-2md inline-flex h-11 w-full items-center justify-start gap-2.5 rounded-full px-5 py-4 font-light outline-1 outline-offset-[-1px] outline-[#ededed] focus:outline-[#4fa097] sm:w-80"
                />
                <Button
                  onClick={handleApplyDiscount}
                  variant="primary"
                  withArrow
                  className="text-2md w-full min-w-32 truncate py-2 pr-2 pl-5 sm:w-36"
                >
                  Apply
                </Button>
              </div>
              {/* Applied Discount Tag */}
              {discount && (
                <motion.div
                  initial={{ opacity: 0, y: 10 }}
                  animate={{ opacity: 1, y: 0 }}
                  exit={{ opacity: 0, y: -10 }}
                  className="inline-flex items-center gap-2 rounded-2xl bg-[#f3f8f6] px-3 py-1 outline outline-[#85b5a5]"
                >
                  <svg width="15" height="15" viewBox="0 0 15 15" fill="none">
                    <path
                      d="M5.33301 5.3335H5.33968M1.33301 3.46683L1.33301 6.44984C1.33301 6.77596 1.33301 6.93902 1.36985 7.09247C1.40251 7.22852 1.45638 7.35858 1.52949 7.47787C1.61194 7.61243 1.72724 7.72773 1.95785 7.95833L7.07027 13.0708C7.8623 13.8628 8.25832 14.2588 8.71497 14.4072C9.11666 14.5377 9.54935 14.5377 9.95104 14.4072C10.4077 14.2588 10.8037 13.8628 11.5958 13.0708L13.0703 11.5962C13.8623 10.8042 14.2583 10.4082 14.4067 9.95153C14.5372 9.54984 14.5372 9.11715 14.4067 8.71546C14.2583 8.2588 13.8623 7.86279 13.0703 7.07075L7.95785 1.95833C7.72724 1.72773 7.61194 1.61243 7.47739 1.52998C7.35809 1.45687 7.22803 1.403 7.09198 1.37034C6.93853 1.3335 6.77547 1.3335 6.44935 1.3335L3.46634 1.3335C2.7196 1.3335 2.34624 1.3335 2.06102 1.47882C1.81014 1.60665 1.60616 1.81063 1.47833 2.06151C1.33301 2.34672 1.33301 2.72009 1.33301 3.46683ZM5.66634 5.3335C5.66634 5.51759 5.5171 5.66683 5.33301 5.66683C5.14891 5.66683 4.99968 5.51759 4.99968 5.3335C4.99968 5.1494 5.14891 5.00016 5.33301 5.00016C5.5171 5.00016 5.66634 5.1494 5.66634 5.3335Z"
                      stroke="#85b5a5"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    />
                  </svg>
                  <span className="text-md font-normal text-[#85b5a5]">{discount.code}</span>
                  <button onClick={handleRemoveDiscount} aria-label="Remove discount code">
                    <X className="size-4 stroke-[#85b5a5] stroke-[1.5]" />
                  </button>
                </motion.div>
              )}
              {/* Summary */}
              <div className="mt-2 space-y-3">
                <div className="flex justify-between">
                  <span className="text-2md font-light text-black">Subtotal</span>
                  <span className="font-dm-sans text-2md font-light text-black">
                    {formatCurrency(subtotal)}
                  </span>
                </div>

                {discount && (
                  <div className="flex justify-between">
                    <span className="text-2md font-light text-[#c13333]">
                      Discount ({discount.code})
                    </span>
                    <span className="font-dm-sans text-2md font-light text-[#c13333]">
                      -{formatCurrency(discountAmount)}
                    </span>
                  </div>
                )}

                <div className="flex justify-between">
                  <span className="text-2md font-light text-black">Delivery</span>
                  <span className="font-dm-sans text-2md font-light text-black">
                    {formatCurrency(deliveryFee)}
                  </span>
                </div>

                <div className="mt-4 border-t border-[#e9e9e9] pt-4">
                  <div className="flex justify-between">
                    <span className="text-2md font-medium text-black">Total</span>
                    <span className="font-dm-sans text-2md font-medium text-black">
                      {formatCurrency(total)}
                    </span>
                  </div>
                </div>
              </div>
              {/* Checkout Button */}
              <Button
                onClick={handleCheckout}
                disabled={items.length === 0}
                aria-label="Proceed to checkout"
                variant="primary"
                withArrow
                className="text-2md w-full min-w-32 truncate py-2.5 pr-2 pl-5"
              >
                Proceed to Checkout
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default Bag;
