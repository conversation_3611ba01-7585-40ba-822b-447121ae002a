"use client";
import Image from "next/image";
import { useRouter } from "next/navigation";
import Badge from "./ui/badge";
import BagIcon from "./icons/bag-icon";
import { useBagStore } from "@/store/bag-store";
import { IProductSingleItem } from "@/types/product";

interface ProductProps {
  hasDiscount?: boolean;
  discountPercentage?: number;
  product: IProductSingleItem;
}

export const ProductCard = ({
  product,
  hasDiscount = true,
  discountPercentage = 30,
}: ProductProps) => {
  const router = useRouter();

  const addItem = useBagStore((state) => state.addItem);

  const handleOnClick = () => {
    router.push(`/products/${product._id}`);
  };

  const handleAddToBag = () => {
    // Add item to bag logic here
    addItem({ _id: product._id, quantity: 1, productId: product, isPrescription: false });
  };

  return (
    <div
      className="group group relative aspect-[4/5] w-[200px] cursor-pointer rounded-[30px] bg-neutral-50 p-2 transition-transform duration-300 hover:scale-[1.02] sm:w-[320px] sm:rounded-[40px] sm:p-3"
      onClick={handleOnClick}
    >
      {hasDiscount && (
        <svg
          width="139"
          height="145"
          viewBox="0 0 139 145"
          fill="none"
          className="absolute top-0 left-0 z-1 h-14 w-14 sm:h-24 sm:w-24"
        >
          <path
            d="M0.381348 45C0.381348 20.1472 20.5285 0 45.3813 0H138.381L0.381348 145V45Z"
            fill="#F2A472"
          />
          <text
            x="15%"
            y="24%"
            dominantBaseline="middle"
            fill="white"
            className="font-dm-sans text-lg font-normal sm:text-2xl"
          >
            {discountPercentage}%
          </text>
          <text
            x="15%"
            y="44%"
            dominantBaseline="middle"
            fill="white"
            className="font-dm-sans text-lg font-normal sm:text-2xl"
          >
            OFF
          </text>
        </svg>
        // <div className="absolute top-0 left-0 z-1 flex size-32 items-center justify-center rounded-tl-[30px] bg-[#f2a472] p-1 text-center text-md font-normal text-white sm:rounded-tl-[40px]">
        //   <p className="text-lg font-normal text-white">{discountPercentage}% OFF</p>
        // </div>
      )}

      <div className="flex h-full w-full flex-col items-center p-3">
        <div className="relative size-[120px] sm:h-[240px] sm:w-[240px]">
          <Image
            width={100}
            height={100}
            quality={100}
            className={`size-[120px] rounded-[24px] object-cover sm:h-[240px] sm:w-[240px]`}
            src={product.main_image || "/images/placeholder.svg"}
            alt={product.name || "Product Image"}
          />
        </div>

        {/* Content container - takes up bottom 30% */}
        <div className="flex w-full flex-col items-start px-1 sm:gap-1 sm:pt-1 sm:pb-3 md:px-4">
          <div className="scrollbar-hide flex w-full items-center gap-2 overflow-x-scroll pt-2">
            {product?.categories?.map((category, index) => (
              <Badge key={index} variant="primary" size="sm" className="whitespace-nowrap">
                {category.name}
              </Badge>
            ))}
          </div>

          <h3 className="text-2md w-full truncate font-normal text-black capitalize sm:block sm:text-lg md:text-xl">
            {product.name}
          </h3>
          <p className="inline-block w-[180px] truncate pb-1 text-xs font-normal text-[#535353] sm:block sm:w-[260px] md:text-sm">
            {product.description}
          </p>
          <div className="flex w-full items-center justify-between">
            <div className="flex flex-col items-start justify-start">
              <p className="font-dm-sans text-xs font-normal text-[#f2a472] line-through">
                ${product.price}
              </p>
              <p className="font-dm-sans text-xl font-medium text-black sm:text-lg">
                ${Math.floor(product.price * (1 - discountPercentage / 100))}
              </p>
            </div>
            <button
              className="rounded-full bg-[#ffffff] p-2 transition-all duration-200 hover:scale-105 hover:bg-white/50 active:scale-95 disabled:cursor-not-allowed disabled:opacity-50 disabled:hover:scale-100 disabled:hover:bg-[#E6FAF7]"
              onClick={(e) => {
                e.preventDefault();
                e.stopPropagation();
                handleAddToBag();
              }}
            >
              <BagIcon className="size-4 stroke-1 md:size-5" />
            </button>
          </div>
        </div>
      </div>
    </div>
  );
};
