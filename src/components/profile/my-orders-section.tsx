"use client";

import MyOrderListItem from "@/components/my-orders/my-order-list-item";
import TabGroup from "@/components/ui/tab-group";
import { orders } from "@/data";
import { motion } from "framer-motion";
import { useCallback, useMemo, useState } from "react";

const MyOrdersSection = () => {
//   const [activeTab, setActiveTab] = useState<string>("preparing");

//   const tabs = [
//     { id: "preparing", label: "Preparing" },
//     { id: "shipped", label: "Shipped" },
//   ];

//   const handleTabChange = useCallback((tabId: string) => {
//     setActiveTab(tabId);
//   }, []);

//   const filteredOrders = orders.filter((order) => order.status === activeTab);

  return (
    <div className="min-h-screen w-full bg-white px-4 py-4">
      {/* Header Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        className="mx-auto mb-4 max-w-2xl text-center sm:mb-6"
      >
        <h1 className="font-henju mb-1 text-2xl font-normal md:text-3xl">My Orders</h1>
        <p className="text-2md font-light text-neutral-600">Manage your orders</p>
      </motion.div>

      {/* Tab Navigation */}
      {/* <div className="mx-auto mb-6 w-full max-w-[400px] sm:mb-8">
        {useMemo(
          () => (
            <TabGroup
              tabs={tabs}
              activeTab={activeTab}
              onTabChange={handleTabChange}
              className="sm:gap-4 md:gap-12"
            />
          ),
          [tabs, activeTab, handleTabChange]
        )}
      </div> */}

      {/* Orders List */}
      <div className="mx-auto w-full max-w-3xl space-y-4 sm:space-y-6">
        {orders.map((order, index) => (
          <MyOrderListItem key={index} order={order} index={index} />
        ))}

        {orders.length === 0 && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="py-8 text-center text-neutral-500 sm:py-12"
          >
            No orders found
          </motion.div>
        )}
      </div>
    </div>
  );
};

export default MyOrdersSection;
