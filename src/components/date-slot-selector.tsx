"use client";

import { cn } from "@/lib/utils";
import { ChevronDown } from "lucide-react";
import { useRouter } from "next/navigation";
import { useEffect, useMemo, useState } from "react";
import ArrowRightIcon from "./icons/arrow-right-icon";
import { Button } from "./ui/button";
import { useGetTreatmentById } from "@/api/category-service";
import { createBooking } from "@/api/booking-service";
import { useToast } from "@/context/toast-context";

// Define types for our data
type DateSlot = {
  day: string;
  date: number;
  month: string;
  fullDate: Date;
  disabled?: boolean;
};

type TimeSlot = {
  time: string;
  disabled?: boolean;
};

interface DateSlotSelectorProps {
  buttonText?: string;
  onSubmit?: (date: string, time: string) => void;
  redirectPath?: string;
  className?: string;
  disabledDates?: string[]; // Format: "YYYY-MM-DD"
  disabledTimes?: string[]; // Format: "HH:MM AM/PM"
  categoryId?: string;
}

export default function DateSlotSelector({
  buttonText = "Start treatment now",
  onSubmit,
  redirectPath = "/questionnaire",
  className,
  disabledDates = ["2023-03-14", "2023-03-21"], // Default disabled dates
  disabledTimes = ["10:30 AM", "11:30 AM"], // Default disabled times
  categoryId ,
}: DateSlotSelectorProps = {}) {
  // Generate dates for the next 4 weeks
  const generateDates = (startDate: Date = new Date()): DateSlot[] => {
    const dates: DateSlot[] = [];
    const dayNames = ["Sun", "Mon", "Tue", "Wed", "Thu", "Fri", "Sat"];
    const monthNames = [
      "Jan",
      "Feb",
      "Mar",
      "Apr",
      "May",
      "Jun",
      "Jul",
      "Aug",
      "Sep",
      "Oct",
      "Nov",
      "Dec",
    ];

    // Start from today and generate dates for the next 28 days (4 weeks)
    for (let i = 0; i < 28; i++) {
      const currentDate = new Date(startDate);
      currentDate.setDate(startDate.getDate() + i);

      const dateString = `${currentDate.getFullYear()}-${String(currentDate.getMonth() + 1).padStart(2, "0")}-${String(currentDate.getDate()).padStart(2, "0")}`;

      dates.push({
        day: dayNames[currentDate.getDay()],
        date: currentDate.getDate(),
        month: monthNames[currentDate.getMonth()],
        fullDate: new Date(currentDate),
        disabled: disabledDates.includes(dateString),
      });
    }

    return dates;
  };

  // Generate time slots from 8:00 AM to 8:00 PM with 30-minute intervals
  const generateTimeSlots = (availableTimes: string[] = []): TimeSlot[] => {
    const slots: TimeSlot[] = [];
    const startHour = 8; // 8:00 AM
    const endHour = 20; // 8:00 PM

    for (let hour = startHour; hour <= endHour; hour++) {
      for (let minute = 0; minute < 60; minute += 30) {
        if (hour === endHour && minute > 0) continue; // Stop at 8:00 PM

        const period = hour < 12 ? "AM" : "PM";
        const displayHour = hour % 12 === 0 ? 12 : hour % 12;
        const timeString = `${displayHour}:${minute === 0 ? "00" : minute} ${period}`;

        // If availableTimes is provided, only enable those slots
        const isDisabled = availableTimes.length > 0 ? !availableTimes.includes(timeString) : disabledTimes.includes(timeString);

        slots.push({
          time: timeString,
          disabled: isDisabled,
        });
      }
    }

    return slots;
  };

  // Initialize state with generated data
  const generatedDates = generateDates();
  const [dates] = useState<DateSlot[]>(generatedDates);
  const [currentWeekIndex, setCurrentWeekIndex] = useState<number>(0);
  const [showAllSlots, setShowAllSlots] = useState<boolean>(false);
  const [isSliding, setIsSliding] = useState<boolean>(false);
  const [showDateError, setShowDateError] = useState<boolean>(false);
  const router = useRouter();
  const { showToast } = useToast();

  // Store the full selected date object instead of just the date number
  const firstAvailableDate = generatedDates.find((date) => !date.disabled) || null;
  const [selectedDateObj, setSelectedDateObj] = useState<DateSlot | null>(firstAvailableDate);

  // Compute selectedDate string for API
  const selectedDate = useMemo(() => selectedDateObj?.fullDate.toISOString().split("T")[0], [selectedDateObj]);

  // Fetch available slots for the selected date/category
  const { categoryDetail } = useGetTreatmentById({
    id: categoryId,
    requestDate: selectedDate,
  });

  // Convert backend slots (e.g., "09:00") to frontend format (e.g., "9:00 AM")
  const convertTo12Hour = (slot: string) => {
    const [hourStr, minute] = slot.split(":");
    let hour = parseInt(hourStr, 10);
    const period = hour >= 12 ? "PM" : "AM";
    hour = hour % 12 === 0 ? 12 : hour % 12;
    return `${hour}:${minute} ${period}`;
  };

  const convertTo24Hour = (slot: string) => {
    const [time, period] = slot.split(" ");
    let [hour, minute] = time.split(":").map(Number);
    if (period === "PM" && hour < 12) {
      hour += 12; // Convert PM to 24-hour format
    } else if (period === "AM" && hour === 12) {
      hour = 0; // Convert 12 AM to 0 hours
    }
    return `${hour}:${minute.toString().padStart(2, "0")}`;
  };

  // Get available time slots from backend (categoryDetail.timeSlots)
  const availableTimeSlots = Array.isArray(categoryDetail?.timeSlots)
    ? categoryDetail.timeSlots.map(convertTo12Hour)
    : [];

  // Generate time slots based on available slots from backend
  const generatedTimeSlots = generateTimeSlots(availableTimeSlots);
  const [timeSlots, setTimeSlots] = useState<TimeSlot[]>(generatedTimeSlots);

  // Find the first available time slot (not disabled)
  const firstAvailableTime = generatedTimeSlots.find((slot) => !slot.disabled)?.time || "8:00 AM";
  const [selectedTime, setSelectedTime] = useState<string>(firstAvailableTime);

  // Update time slots when availableTimeSlots or selectedDateObj change
  useEffect(() => {
    const newSlots = generateTimeSlots(availableTimeSlots);
    setTimeSlots(newSlots);
    // Reset selected time if it's not available anymore
    if (!availableTimeSlots.includes(selectedTime)) {
      const firstAvailable = newSlots.find((slot) => !slot.disabled)?.time || "8:00 AM";
      setSelectedTime(firstAvailable);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [categoryDetail?.timeSlots, selectedDateObj]);

  // Function to find which week contains a specific date
  const findWeekForDate = (dateObj: DateSlot): number => {
    const dateIndex = dates.findIndex((d) => d.date === dateObj.date && d.month === dateObj.month);
    if (dateIndex === -1) return 0;
    return Math.floor(dateIndex / 7);
  };

  // Enhanced navigation functions with animation states
  const goToPreviousWeek = () => {
    if (currentWeekIndex > 0) {
      setIsSliding(true);
      setTimeout(() => {
        setCurrentWeekIndex(currentWeekIndex - 1);
        setIsSliding(false);
      }, 300);
    }
  };

  const goToNextWeek = () => {
    if (currentWeekIndex < 3) {
      // 4 weeks total (0-3)
      setIsSliding(true);
      setTimeout(() => {
        setCurrentWeekIndex(currentWeekIndex + 1);
        setIsSliding(false);
      }, 300);
    }
  };

  // Function to navigate to a specific week
  const goToWeek = (weekIndex: number) => {
    if (weekIndex >= 0 && weekIndex <= 3 && weekIndex !== currentWeekIndex) {
      setIsSliding(true);
      setTimeout(() => {
        setCurrentWeekIndex(weekIndex);
        setIsSliding(false);
      }, 300);
    }
  };

  // Navigate to the week containing the first available date when component mounts
  useEffect(() => {
    if (firstAvailableDate) {
      const weekIndex = findWeekForDate(firstAvailableDate);
      setCurrentWeekIndex(weekIndex);
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  // Function to toggle showing all time slots
  const toggleShowAllSlots = () => {
    setShowAllSlots(!showAllSlots);
  };

  // Function to handle submission
  const handleSubmit = async () => {
    try {
      if (!selectedDateObj) {
        setShowDateError(true);
        return;
      }
      const formattedDate = `${selectedDateObj.day}, ${selectedDateObj.date} ${selectedDateObj.month}`;
      if (onSubmit) {
        onSubmit(formattedDate, selectedTime);
      } else {
        const time = selectedDateObj.fullDate.toISOString().split("T")[0] + "T" + convertTo24Hour(selectedTime) + ":00.000Z";
        console.log(`Selected date: ${time}`);
        const response = await createBooking({
          category: categoryId || "",
          bookingTime: time,
          duration: 30, // Assuming a default duration of 30 minutes
        });
        console.log("Booking created:", response);
        router.push(redirectPath);
      }
    } catch (error: any) {
      const errorData = error?.response?.data;
      console.error("Error submitting date and time:", error);
      if (Array.isArray(errorData?.message)) {
        showToast(errorData.message[0], "error");
      }
      else if (errorData?.message) {
        showToast(errorData.message, "error");
      }
      setShowDateError(true);
    }
  };

  // Get current week's dates
  const currentWeekDates = dates.slice(currentWeekIndex * 7, (currentWeekIndex + 1) * 7);

  // Display only first 8 time slots if not showing all
  const visibleTimeSlots = showAllSlots ? timeSlots : timeSlots.slice(0, 8);

  return (
    <div className={cn("mx-auto max-w-3xl px-2 sm:px-4 lg:px-6", className)}>
      {/* Date selector */}
      <div className="relative mb-6 sm:mb-8">
        <div className="flex items-center">
          <button
            onClick={goToPreviousWeek}
            disabled={currentWeekIndex === 0}
            className={cn(
              "absolute left-0 z-10 flex size-8 -translate-x-1/2 items-center justify-center rounded-full border-gray-200 bg-white shadow-sm transition-transform duration-200 hover:border md:size-10 md:-translate-x-[60px] lg:size-12",
              currentWeekIndex > 0
                ? "hover:scale-105 hover:shadow-md active:scale-95"
                : "cursor-not-allowed opacity-50"
            )}
            aria-label="Previous week"
          >
            <ArrowRightIcon
              className="size-4 rotate-180 text-gray-500 md:size-5 lg:size-6"
              color="#000"
            />
          </button>

          <div className="w-full overflow-hidden">
            <div className="scrollbar-hide flex overflow-x-auto">
              <div className="flex min-w-full">
                {currentWeekDates.map((date) => (
                  <div
                    key={`${date.day}-${date.date}-${date.month}`}
                    className="flex-1 py-2 text-center text-md text-[#85b5a5] sm:py-3 sm:text-2md"
                  >
                    {date.day}
                  </div>
                ))}
              </div>
            </div>

            <div
              className={cn(
                "scrollbar-hide flex overflow-x-auto transition-transform duration-300",
                isSliding && "translate-x-full transform"
              )}
            >
              <div className="flex min-w-full gap-2">
                {currentWeekDates.map((date) => (
                  <button
                    key={`${date.date}-${date.month}`}
                    disabled={date.disabled}
                    onClick={() => {
                      if (!date.disabled) {
                        // Set the selected date
                        setSelectedDateObj(date);
                        setShowDateError(false);

                        // Find which week contains this date and navigate to it if needed
                        const weekIndex = findWeekForDate(date);
                        goToWeek(weekIndex);
                      }
                    }}
                    className={cn(
                      "flex flex-1 flex-col items-center justify-center rounded-2xl py-3",
                      selectedDateObj &&
                        date.date === selectedDateObj.date &&
                        date.month === selectedDateObj.month
                        ? "border-primary border bg-[#85b5a5]/10 text-black"
                        : "bg-white hover:bg-gray-50",
                      date.disabled ? "cursor-not-allowed text-gray-300" : "",
                      "transform text-2md font-normal"
                    )}
                  >
                    <span className="text-2md font-light sm:text-lg">{date.date}</span>
                    <span className="text-md font-light sm:text-2md">{date.month}</span>
                  </button>
                ))}
              </div>
            </div>
          </div>

          <button
            onClick={goToNextWeek}
            disabled={currentWeekIndex === 3}
            className={cn(
              "absolute right-0 z-10 flex size-8 translate-x-1/2 items-center justify-center rounded-full border-gray-200 bg-white shadow-sm transition-transform duration-200 hover:border md:size-10 md:translate-x-[60px] lg:size-12",
              currentWeekIndex < 3
                ? "hover:scale-105 hover:shadow-md active:scale-95"
                : "cursor-not-allowed opacity-50"
            )}
            aria-label="Next week"
          >
            <ArrowRightIcon className="size-4 text-gray-500 md:size-5 lg:size-6" color="#000" />
          </button>
        </div>
      </div>

      {/* Time slots */}
      <div className="mb-4 grid grid-cols-2 gap-2 sm:mb-6 sm:grid-cols-3 sm:gap-4 lg:grid-cols-4">
        {visibleTimeSlots.map((slot, index) => (
          <button
            key={slot.time}
            disabled={slot.disabled}
            onClick={() => !slot.disabled && setSelectedTime(slot.time)}
            className={cn(
              "rounded-full border border-gray-200 py-3 text-center text-2md font-light transition-all duration-300 sm:py-4 sm:text-2md",
              selectedTime === slot.time
                ? "border-primary scale-105 border bg-[#85b5a5]/10 text-black"
                : "bg-white hover:bg-gray-50",
              slot.disabled ? "cursor-not-allowed text-gray-300" : "",
              "transform hover:scale-105 active:scale-100",
              "animate-fadeIn"
            )}
            style={{
              animationDelay: `${index * 50}ms`,
            }}
          >
            {slot.time}
          </button>
        ))}
      </div>

      {/* View all slots button */}
      {timeSlots.length > 8 && (
        <div className="mb-6 text-center sm:mb-8">
          <button
            onClick={toggleShowAllSlots}
            className="inline-flex items-center text-2md font-light text-black transition-colors duration-200 hover:text-[#4AACB5] sm:text-2md"
          >
            {showAllSlots ? "Show fewer slots" : "View all slots"}
            <ChevronDown
              className={cn(
                "ml-2 h-3 w-3 transition-transform duration-300 sm:h-4 sm:w-4",
                showAllSlots ? "rotate-180" : ""
              )}
            />
          </button>
        </div>
      )}

      {/* Date selection indicator */}
      {showDateError && !selectedDateObj && (
        <div className="mb-4 animate-pulse text-center text-2md text-red-500">
          Please select a date to continue
        </div>
      )}

      {/* Action button */}
      <Button onClick={handleSubmit} variant="primary" fullWidth withArrow className="px-2 py-2">
        {buttonText}
      </Button>
    </div>
  );
}
